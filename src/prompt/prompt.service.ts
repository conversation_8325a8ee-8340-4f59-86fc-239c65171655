import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { SavePromptDto } from './dto/save-prompt.dto';

@Injectable()
export class PromptService {
  constructor(private readonly prismaService: PrismaService) {}
  async savePrompt(savePromptDto: SavePromptDto) {
    try {
      const prompt = await this.prismaService.promptHistory.create({
        data: savePromptDto,
      });
      return {
        message: 'Prompt saved successfully',
        data: prompt,
        statusCode: 201,
      };
    } catch (error) {
      throw new Error(error as string);
    }
  }
}
